'use client';

import { useState } from 'react';

interface SimpleRichTextEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  height?: number;
  disabled?: boolean;
  className?: string;
}

export default function SimpleRichTextEditor({
  value,
  onChange,
  placeholder = 'İçerik yazın...',
  height = 300,
  disabled = false,
  className = ''
}: SimpleRichTextEditorProps) {
  const [isPreview, setIsPreview] = useState(false);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  const insertFormatting = (before: string, after: string = '') => {
    const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    
    const newText = value.substring(0, start) + before + selectedText + after + value.substring(end);
    onChange(newText);
    
    // Cursor pozisyonunu ayarla
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + before.length, start + before.length + selectedText.length);
    }, 0);
  };

  const formatText = (format: string) => {
    switch (format) {
      case 'bold':
        insertFormatting('**', '**');
        break;
      case 'italic':
        insertFormatting('*', '*');
        break;
      case 'underline':
        insertFormatting('<u>', '</u>');
        break;
      case 'list':
        insertFormatting('\n- ');
        break;
      case 'numberedList':
        insertFormatting('\n1. ');
        break;
    }
  };

  const renderPreview = (text: string) => {
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n- (.*?)(?=\n|$)/g, '<li>$1</li>')
      .replace(/\n\d+\. (.*?)(?=\n|$)/g, '<li>$1</li>')
      .replace(/\n/g, '<br>');
  };

  return (
    <div className={`simple-rich-text-editor border border-gray-300 rounded-md ${className}`}>
      {/* Toolbar */}
      <div className="border-b border-gray-200 p-2 flex flex-wrap gap-1 items-center">
        <button
          type="button"
          onClick={() => formatText('bold')}
          className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
          disabled={disabled}
          title="Kalın (Ctrl+B)"
        >
          <strong>B</strong>
        </button>
        <button
          type="button"
          onClick={() => formatText('italic')}
          className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
          disabled={disabled}
          title="İtalik (Ctrl+I)"
        >
          <em>I</em>
        </button>
        <button
          type="button"
          onClick={() => formatText('underline')}
          className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
          disabled={disabled}
          title="Altı çizili"
        >
          <u>U</u>
        </button>
        <div className="w-px bg-gray-300 mx-1"></div>
        <button
          type="button"
          onClick={() => formatText('list')}
          className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
          disabled={disabled}
          title="Madde listesi"
        >
          • Liste
        </button>
        <button
          type="button"
          onClick={() => formatText('numberedList')}
          className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
          disabled={disabled}
          title="Numaralı liste"
        >
          1. Liste
        </button>
        <div className="w-px bg-gray-300 mx-1"></div>
        <button
          type="button"
          onClick={() => setIsPreview(!isPreview)}
          className={`px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 ${isPreview ? 'bg-blue-100 text-blue-700' : ''}`}
          disabled={disabled}
          title="Önizleme"
        >
          {isPreview ? 'Düzenle' : 'Önizleme'}
        </button>
      </div>

      {/* Editor/Preview */}
      <div style={{ height: height - 50 }}>
        {isPreview ? (
          <div 
            className="p-3 overflow-auto h-full"
            style={{ 
              direction: 'ltr',
              textAlign: 'left'
            }}
            dangerouslySetInnerHTML={{ __html: renderPreview(value) }}
          />
        ) : (
          <textarea
            value={value}
            onChange={handleTextChange}
            placeholder={placeholder}
            disabled={disabled}
            className="w-full h-full p-3 border-0 resize-none focus:outline-none"
            style={{ 
              direction: 'ltr',
              textAlign: 'left',
              fontFamily: 'inherit',
              fontSize: '14px'
            }}
            dir="ltr"
          />
        )}
      </div>
      
      {/* Help text */}
      <div className="px-3 py-1 text-xs text-gray-500 border-t border-gray-200 bg-gray-50">
        İpucu: **kalın**, *italik*, - madde listesi, 1. numaralı liste
      </div>
    </div>
  );
}
