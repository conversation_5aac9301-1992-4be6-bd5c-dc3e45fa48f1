'use client';

import { useRef, useEffect, useState } from 'react';

interface RichTextEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  height?: number;
  disabled?: boolean;
  className?: string;
}

export default function RichTextEditor({
  value,
  onChange,
  placeholder = 'İçerik yazın...',
  height = 300,
  disabled = false,
  className = ''
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const handleCommand = (command: string, value?: string) => {
    if (editorRef.current) {
      editorRef.current.focus();
      document.execCommand(command, false, value);
      onChange(editorRef.current.innerHTML);
    }
  };

  const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
    const target = e.currentTarget;
    onChange(target.innerHTML);
  };

  // <PERSON>lk yükleme için value'yu set et
  useEffect(() => {
    if (editorRef.current && !isInitialized) {
      editorRef.current.innerHTML = value || '';
      setIsInitialized(true);
    }
  }, [value, isInitialized]);

  return (
    <div className={`rich-text-editor border border-gray-300 rounded-md ${className}`}>
      {/* Toolbar */}
      <div className="border-b border-gray-200 p-2 flex flex-wrap gap-1">
        <button
          type="button"
          onClick={() => handleCommand('bold')}
          className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
          disabled={disabled}
        >
          <strong>B</strong>
        </button>
        <button
          type="button"
          onClick={() => handleCommand('italic')}
          className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
          disabled={disabled}
        >
          <em>I</em>
        </button>
        <button
          type="button"
          onClick={() => handleCommand('underline')}
          className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
          disabled={disabled}
        >
          <u>U</u>
        </button>
        <div className="w-px bg-gray-300 mx-1"></div>
        <button
          type="button"
          onClick={() => handleCommand('insertUnorderedList')}
          className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
          disabled={disabled}
        >
          • Liste
        </button>
        <button
          type="button"
          onClick={() => handleCommand('insertOrderedList')}
          className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
          disabled={disabled}
        >
          1. Liste
        </button>
        <div className="w-px bg-gray-300 mx-1"></div>
        <button
          type="button"
          onClick={() => handleCommand('justifyLeft')}
          className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
          disabled={disabled}
        >
          ←
        </button>
        <button
          type="button"
          onClick={() => handleCommand('justifyCenter')}
          className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
          disabled={disabled}
        >
          ↔
        </button>
        <button
          type="button"
          onClick={() => handleCommand('justifyRight')}
          className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
          disabled={disabled}
        >
          →
        </button>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable={!disabled}
        onInput={handleInput}
        className="p-3 min-h-[200px] focus:outline-none"
        style={{
          height: height - 50,
          direction: 'ltr',
          textAlign: 'left',
          unicodeBidi: 'plaintext'
        }}
        data-placeholder={placeholder}
        dir="ltr"
        suppressContentEditableWarning={true}
      />
    </div>
  );
}

// Basit metin editörü (fallback)
export function SimpleTextEditor({
  value,
  onChange,
  placeholder = 'İçerik yazın...',
  disabled = false,
  className = '',
  rows = 6
}: {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  rows?: number;
}) {
  return (
    <textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      rows={rows}
      className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 ${className}`}
    />
  );
}

// HTML içeriği güvenli şekilde göstermek için
export function RichTextDisplay({
  content,
  className = ''
}: {
  content: string;
  className?: string;
}) {
  // HTML tag'lerini temizle ve sadece metin göster
  const cleanText = content
    .replace(/<[^>]*>/g, '') // HTML tag'lerini kaldır
    .replace(/&nbsp;/g, ' ') // &nbsp; karakterlerini boşlukla değiştir
    .replace(/&amp;/g, '&') // &amp; karakterlerini & ile değiştir
    .replace(/&lt;/g, '<') // &lt; karakterlerini < ile değiştir
    .replace(/&gt;/g, '>') // &gt; karakterlerini > ile değiştir
    .trim();

  return (
    <div
      className={`rich-text-display ${className}`}
      style={{
        direction: 'ltr',
        textAlign: 'left',
        whiteSpace: 'pre-wrap'
      }}
    >
      {cleanText}
    </div>
  );
}
